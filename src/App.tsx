// React
import { useEffect, useRef, useState } from "react";
// Bootstrap
import Button from "react-bootstrap/Button";
import {
  PlusLg,
  GearFill,
  PauseFill,
  PlayFill,
  StopFill,
  Magic,
} from "react-bootstrap-icons";
import Spinner from "react-bootstrap/Spinner";
// Components
import Cards from "./components/Cards/Cards";
import AddCardModal from "./modals/AddCardModal";
// Styles
import "./App.css";
// Interfaces
import { Card, CardContent, LangCardsSettings } from "./Interfaces";
// Utilities
import { translate } from "./utilites/translate";
import { PlaylistPlayer } from "./utilites/PlaylistPlayer";
import { saveCards, saveSettings } from "./utilites/localStorage";
import SettingsModal from "./modals/SettingsModal";
import { generateRandomWordCard } from "./utilites/generators";
import { useDispatch, useSelector } from "react-redux";
import {
  addNew<PERSON><PERSON>,
  deleteCard,
  LangCardsState,
  updateCard,
} from "./store/store/cards-store";
import { setSettings, SettingsState } from "./store/store/settings-store";

export default function App() {
  const settings = useSelector((state: SettingsState) => state.settings);
  const cards = useSelector((state: LangCardsState) => state.cards);
  const dispatch = useDispatch();
  const [showAddCardModal, setShowAddCardModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [currentPlayingCardId, setCurrentPlayingCardId] = useState<
    number | null
  >(null);
  const [isPlaylistPlaying, setIsPlaylistPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const player = useRef(new PlaylistPlayer());

  const onAddCard = async (content: string) => {
    setShowAddCardModal(false);
    const original = content;
    const originalLang = settings.originalLang;
    const translatedLang = settings.translatedLang;
    setIsLoading(true);
    const translated = await translate(
      original,
      originalLang.isoLang,
      translatedLang.isoLang
    );
    setIsLoading(false);
    const card: Card = {
      id: Date.now(),
      content: {
        original: original,
        originalLang,
        translated,
        translatedLang: translatedLang,
      },
    };
    dispatch(addNewCard(card));
  };

  const editCard = async (id: number, newContent: string) => {
    const original = newContent;
    const originalLang = settings.originalLang;
    const translatedLang = settings.translatedLang;
    setIsLoading(true);
    const translated = await translate(
      original,
      originalLang.isoLang,
      translatedLang.isoLang
    );
    setIsLoading(false);
    const updatedCardContent: CardContent = {
      original: original,
      originalLang,
      translated,
      translatedLang: translatedLang,
    };
    dispatch(updateCard({ id, content: updatedCardContent }));
  };

  const onDeleteCard = (id: number) => {
    dispatch(deleteCard(id));
  };

  const onGenerateCard = async () => {
    const randomCard = await generateRandomWordCard(settings);
    dispatch(addNewCard(randomCard));
  };

  const onSaveSettings = (settings: LangCardsSettings) => {
    setShowSettingsModal(false);
    dispatch(setSettings(settings));
  };

  const handlePlay = () => {
    if (cards.length === 0) return;
    player.current.play(setCurrentPlayingCardId, true);
    setIsPlaylistPlaying(true);
  };
  const handlePause = () => {
    if (cards.length === 0) return;
    player.current.pause();
    setIsPlaylistPlaying(false);
  };
  const handleStop = () => {
    player.current.stop(setCurrentPlayingCardId);
  };

  useEffect(() => {
    player.current.initialize(cards, setCurrentPlayingCardId, () =>
      setIsPlaylistPlaying(false)
    );
  }, [cards]);

  // Save Cards on changes
  useEffect(() => saveCards(cards), [cards]);

  // Save Settings on changes
  useEffect(() => saveSettings(settings), [settings]);

  return (
    <main className="h-100 d-flex flex-column align-items-center p-3">
      <h1>Lang-Cards</h1>
      {/* Settings Icon */}
      <GearFill
        className="settings-icon"
        onClick={() => setShowSettingsModal(true)}
      ></GearFill>
      {/* Main Section */}
      <section className="main-section d-flex flex-column p-3 rounded-4 gap-3">
        {/* Top Actions */}
        <div className="d-flex justify-content-between gap-2">
          {/* Add Card Action */}
          <Button
            variant="primary"
            className="add-card-btn"
            onClick={() => setShowAddCardModal(true)}
          >
            <PlusLg className="add-card-icon"></PlusLg>
            <span>Add Card</span>
          </Button>
          {/* Auto Generate Card Action */}
          <Button
            variant="success"
            className="auto-generate-card-btn"
            onClick={onGenerateCard}
          >
            <Magic className="generate-card-icon"></Magic>
            <span>Generate</span>
          </Button>
        </div>
        {/* Cards List */}
        <Cards
          cards={cards}
          currentlyPlayingCardId={currentPlayingCardId}
          onEdit={editCard}
          onDelete={onDeleteCard}
        ></Cards>
        {/* Loading Indicator */}
        {isLoading && (
          <Spinner animation="border" variant="warning" className="spinner" />
        )}
      </section>
      {/* Playlist Controls */}
      <section className="playlist-controls d-flex gap-3 mt-3">
        {isPlaylistPlaying ? (
          <PauseFill size={48} onClick={handlePause} />
        ) : (
          <PlayFill size={48} onClick={handlePlay} />
        )}
        <StopFill size={48} onClick={handleStop} />
      </section>
      {/* Add Card Modal */}
      <AddCardModal
        show={showAddCardModal}
        onCancel={() => setShowAddCardModal(false)}
        onSave={onAddCard}
      ></AddCardModal>
      <SettingsModal
        show={showSettingsModal}
        currentSettings={settings}
        onCancel={() => setShowSettingsModal(false)}
        onSave={onSaveSettings}
      ></SettingsModal>
    </main>
  );
}
