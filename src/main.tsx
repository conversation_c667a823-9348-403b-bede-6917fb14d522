// React
import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
// Redux
import { Provider } from "react-redux";
import cardsStore from "./store/store/cards-store.tsx";
// Components
import App from "./App.tsx";
// Styles
import "./index.css";
// Bootstrap styles
import "bootstrap/dist/css/bootstrap.min.css";
import settingsStore from "./store/store/settings-store.tsx";

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <Provider store={settingsStore}>
      <Provider store={cardsStore}>
        <App />
      </Provider>
    </Provider>
  </StrictMode>
);
