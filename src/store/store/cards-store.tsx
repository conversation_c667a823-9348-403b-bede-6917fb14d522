// Redux
import { configureStore } from "@reduxjs/toolkit";
// Constants
import { Card } from "../../Interfaces.js";
import { getCards } from "../../utilites/localStorage.js";
// Actions
const ADD_CARD = "ADD_CARD";
const UPDATE_CARD = "UPDATE_CARD";
const DELETE_CARD = "DELETE_CARD";
const SET_CARDS = "SET_CARDS";

const initialState = {
  cards: getCards(),
};

export function addNewCard(card: Card) {
  return {
    type: ADD_CARD,
    data: card,
  };
}

export function updateCard(card: Card) {
  return {
    type: UPDATE_CARD,
    data: card,
  };
}

export function deleteCard(id: number) {
  return {
    type: DELETE_CARD,
    data: {
      id,
    },
  };
}

export function setCards(cards: Card[]) {
  return {
    type: SET_CARDS,
    data: cards,
  };
}

interface AddCardAction {
  type: typeof ADD_CARD;
  data: Card;
}

interface EditCardAction {
  type: typeof UPDATE_CARD;
  data: Card;
}

interface DeleteCardAction {
  type: typeof DELETE_CARD;
  data: {
    id: number;
  };
}

interface SetCardsAction {
  type: typeof SET_CARDS;
  data: Card[];
}

type CardAction =
  | AddCardAction
  | EditCardAction
  | DeleteCardAction
  | SetCardsAction;

export interface LangCardsState {
  cards: Card[];
}

const cardsReducer = (
  state: LangCardsState = initialState,
  action: CardAction
): LangCardsState => {
  switch (action.type) {
    case ADD_CARD:
      return {
        ...state,
        cards: [...state.cards, action.data],
      };
    case UPDATE_CARD:
      return {
        ...state,
        cards: state.cards.map((c: Card) => {
          if (c.id === action.data.id) {
            return {
              ...c,
              ...action.data,
            };
          } else {
            return c;
          }
        }),
      };
    case DELETE_CARD:
      return {
        ...state,
        cards: state.cards.filter((c: Card) => c.id !== action.data.id),
      };
    case SET_CARDS:
      return {
        ...state,
        cards: action.data,
      };
    default:
      return state;
  }
};

const cardsStore = configureStore({
  reducer: cardsReducer,
});
export default cardsStore;
