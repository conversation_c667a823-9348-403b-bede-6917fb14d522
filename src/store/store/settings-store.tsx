// Redux
import { configureStore } from "@reduxjs/toolkit";
import { getSettings } from "../../utilites/localStorage";
import { LangCardsSettings } from "../../Interfaces";
// Constants
const SET_SETTINGS = "SET_SETTINGS";
// Interfaces
interface SetSettingsAction {
  type: typeof SET_SETTINGS;
  data: LangCardsSettings;
}

export interface SettingsState {
  settings: LangCardsSettings;
}

type SettingsAction = SetSettingsAction;

const initialState: SettingsState = {
  settings: getSettings(),
};

export function setSettings(settings: LangCardsSettings) {
  return {
    type: SET_SETTINGS,
    data: settings,
  };
}

const settingsReducer = (
  state: SettingsState = initialState,
  action: SettingsAction
): SettingsState => {
  switch (action.type) {
    case SET_SETTINGS:
      return {
        ...state,
        settings: action.data,
      };
    default:
      return state;
  }
};
